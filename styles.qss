/* ملف الأنماط للتطبيق - تصميم عصري مسطح */

/* النافذة الرئيسية */
QMainWindow {
    background-color: #f8f9fa;
    color: #2c3e50;
    font-family: "Segoe UI", "<PERSON><PERSON><PERSON>", "Arial";
    font-size: 12px;
}

/* الشريط الجانبي */
#sidebar {
    background-color: #34495e;
    border: none;
    min-width: 250px;
    max-width: 250px;
}

/* أزرار الشريط الجانبي */
#sidebar QPushButton {
    background-color: transparent;
    color: #ecf0f1;
    border: none;
    padding: 15px 20px;
    text-align: right;
    font-size: 14px;
    font-weight: bold;
    border-radius: 0px;
}

#sidebar QPushButton:hover {
    background-color: #3498db;
    color: white;
}

#sidebar QPushButton:pressed {
    background-color: #2980b9;
}

#sidebar QPushButton:checked {
    background-color: #3498db;
    color: white;
    border-left: 4px solid #e74c3c;
}

/* المنطقة الرئيسية */
#main_content {
    background-color: #ffffff;
    border: none;
    border-radius: 10px;
    margin: 10px;
}

/* العناوين */
QLabel#title {
    font-size: 24px;
    font-weight: bold;
    color: #2c3e50;
    padding: 20px;
    background-color: transparent;
}

QLabel#subtitle {
    font-size: 16px;
    color: #7f8c8d;
    padding: 10px 20px;
}

/* البطاقات */
QFrame#card {
    background-color: #ffffff;
    border: 1px solid #ecf0f1;
    border-radius: 10px;
    padding: 20px;
    margin: 10px;
}

QFrame#stat_card {
    background-color: #3498db;
    border: none;
    border-radius: 15px;
    padding: 15px;
    margin: 5px;
    min-width: 180px;
    min-height: 100px;
}

QFrame#stat_card QLabel {
    color: white;
    font-weight: bold;
}

/* حقول الإدخال */
QLineEdit {
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    background-color: #ffffff;
    color: #2c3e50;
}

QLineEdit:focus {
    border-color: #3498db;
    outline: none;
}

QLineEdit:hover {
    border-color: #95a5a6;
}

/* القوائم المنسدلة */
QComboBox {
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    background-color: #ffffff;
    color: #2c3e50;
    min-height: 20px;
}

QComboBox:focus {
    border-color: #3498db;
}

QComboBox::drop-down {
    border: none;
    width: 30px;
}

QComboBox::down-arrow {
    image: url(icons/arrow_down.png);
    width: 12px;
    height: 12px;
}

QComboBox QAbstractItemView {
    border: 1px solid #bdc3c7;
    border-radius: 8px;
    background-color: #ffffff;
    selection-background-color: #3498db;
    selection-color: white;
    padding: 5px;
}

/* مربعات النص الكبيرة */
QTextEdit {
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 14px;
    background-color: #ffffff;
    color: #2c3e50;
}

QTextEdit:focus {
    border-color: #3498db;
}

/* الأزرار */
QPushButton {
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 12px 25px;
    font-size: 14px;
    font-weight: bold;
    min-height: 20px;
}

QPushButton:hover {
    background-color: #2980b9;
}

QPushButton:pressed {
    background-color: #21618c;
}

QPushButton:disabled {
    background-color: #bdc3c7;
    color: #7f8c8d;
}

/* أزرار خاصة */
QPushButton#success_btn {
    background-color: #27ae60;
}

QPushButton#success_btn:hover {
    background-color: #229954;
}

QPushButton#danger_btn {
    background-color: #e74c3c;
}

QPushButton#danger_btn:hover {
    background-color: #c0392b;
}

QPushButton#warning_btn {
    background-color: #f39c12;
}

QPushButton#warning_btn:hover {
    background-color: #e67e22;
}

/* الجداول */
QTableWidget {
    border: 1px solid #bdc3c7;
    border-radius: 8px;
    background-color: #ffffff;
    alternate-background-color: #f8f9fa;
    gridline-color: #ecf0f1;
    selection-background-color: #3498db;
    selection-color: white;
}

QTableWidget::item {
    padding: 12px;
    border: none;
}

QTableWidget::item:selected {
    background-color: #3498db;
    color: white;
}

QHeaderView::section {
    background-color: #34495e;
    color: white;
    padding: 12px;
    border: none;
    font-weight: bold;
}

/* شريط التمرير */
QScrollBar:vertical {
    border: none;
    background-color: #ecf0f1;
    width: 12px;
    border-radius: 6px;
}

QScrollBar::handle:vertical {
    background-color: #bdc3c7;
    border-radius: 6px;
    min-height: 20px;
}

QScrollBar::handle:vertical:hover {
    background-color: #95a5a6;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    border: none;
    background: none;
}

/* التقويم */
QCalendarWidget {
    border: 1px solid #bdc3c7;
    border-radius: 8px;
    background-color: #ffffff;
}

QCalendarWidget QToolButton {
    background-color: #34495e;
    color: white;
    border: none;
    padding: 8px;
    font-weight: bold;
}

QCalendarWidget QToolButton:hover {
    background-color: #3498db;
}

QCalendarWidget QAbstractItemView {
    selection-background-color: #3498db;
    selection-color: white;
}

/* رسائل التنبيه */
QMessageBox {
    background-color: #ffffff;
    color: #2c3e50;
}

QMessageBox QPushButton {
    min-width: 80px;
    padding: 8px 16px;
}

/* شريط التقدم */
QProgressBar {
    border: 2px solid #bdc3c7;
    border-radius: 8px;
    background-color: #ecf0f1;
    text-align: center;
    font-weight: bold;
    color: #2c3e50;
}

QProgressBar::chunk {
    background-color: #3498db;
    border-radius: 6px;
}

/* التبويبات */
QTabWidget::pane {
    border: 1px solid #bdc3c7;
    border-radius: 8px;
    background-color: #ffffff;
}

QTabBar::tab {
    background-color: #ecf0f1;
    color: #2c3e50;
    padding: 12px 20px;
    margin-right: 2px;
    border-top-left-radius: 8px;
    border-top-right-radius: 8px;
}

QTabBar::tab:selected {
    background-color: #3498db;
    color: white;
}

QTabBar::tab:hover {
    background-color: #bdc3c7;
}
