import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Optional, Tuple

class ExpenseDatabase:
    """كلاس للتعامل مع قاعدة بيانات المصاريف"""
    
    def __init__(self, db_path: str = "expenses.db"):
        self.db_path = db_path
        self.init_database()
    
    def init_database(self):
        """إنشاء قاعدة البيانات والجداول"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.cursor()
            
            # إنشاء جدول المصاريف
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS expenses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    amount REAL NOT NULL,
                    category TEXT NOT NULL,
                    date TEXT NOT NULL,
                    notes TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # إنشاء جدول التصنيفات
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE NOT NULL,
                    color TEXT DEFAULT '#3498db'
                )
            ''')
            
            # إدراج التصنيفات الافتراضية
            default_categories = [
                ('طعام وشراب', '#e74c3c'),
                ('مواصلات', '#3498db'),
                ('ترفيه', '#9b59b6'),
                ('صحة', '#2ecc71'),
                ('تسوق', '#f39c12'),
                ('فواتير', '#34495e'),
                ('تعليم', '#1abc9c'),
                ('أخرى', '#95a5a6')
            ]
            
            cursor.executemany('''
                INSERT OR IGNORE INTO categories (name, color) VALUES (?, ?)
            ''', default_categories)
            
            conn.commit()
    
    def add_expense(self, name: str, amount: float, category: str, 
                   date: str, notes: str = "") -> bool:
        """إضافة مصروف جديد"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO expenses (name, amount, category, date, notes)
                    VALUES (?, ?, ?, ?, ?)
                ''', (name, amount, category, date, notes))
                conn.commit()
                return True
        except Exception as e:
            print(f"خطأ في إضافة المصروف: {e}")
            return False
    
    def get_expenses(self, start_date: str = None, end_date: str = None) -> List[Dict]:
        """جلب المصاريف مع إمكانية التصفية بالتاريخ"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if start_date and end_date:
                    cursor.execute('''
                        SELECT * FROM expenses 
                        WHERE date BETWEEN ? AND ?
                        ORDER BY date DESC
                    ''', (start_date, end_date))
                else:
                    cursor.execute('SELECT * FROM expenses ORDER BY date DESC')
                
                columns = [description[0] for description in cursor.description]
                expenses = []
                
                for row in cursor.fetchall():
                    expense = dict(zip(columns, row))
                    expenses.append(expense)
                
                return expenses
        except Exception as e:
            print(f"خطأ في جلب المصاريف: {e}")
            return []
    
    def update_expense(self, expense_id: int, name: str, amount: float, 
                      category: str, date: str, notes: str = "") -> bool:
        """تحديث مصروف موجود"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    UPDATE expenses 
                    SET name=?, amount=?, category=?, date=?, notes=?
                    WHERE id=?
                ''', (name, amount, category, date, notes, expense_id))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"خطأ في تحديث المصروف: {e}")
            return False
    
    def delete_expense(self, expense_id: int) -> bool:
        """حذف مصروف"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('DELETE FROM expenses WHERE id=?', (expense_id,))
                conn.commit()
                return cursor.rowcount > 0
        except Exception as e:
            print(f"خطأ في حذف المصروف: {e}")
            return False
    
    def get_categories(self) -> List[Dict]:
        """جلب جميع التصنيفات"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT * FROM categories ORDER BY name')
                
                columns = [description[0] for description in cursor.description]
                categories = []
                
                for row in cursor.fetchall():
                    category = dict(zip(columns, row))
                    categories.append(category)
                
                return categories
        except Exception as e:
            print(f"خطأ في جلب التصنيفات: {e}")
            return []
    
    def get_expenses_by_category(self, start_date: str = None, 
                               end_date: str = None) -> Dict[str, float]:
        """جلب المصاريف مجمعة حسب التصنيف"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if start_date and end_date:
                    cursor.execute('''
                        SELECT category, SUM(amount) as total
                        FROM expenses 
                        WHERE date BETWEEN ? AND ?
                        GROUP BY category
                        ORDER BY total DESC
                    ''', (start_date, end_date))
                else:
                    cursor.execute('''
                        SELECT category, SUM(amount) as total
                        FROM expenses 
                        GROUP BY category
                        ORDER BY total DESC
                    ''')
                
                result = {}
                for row in cursor.fetchall():
                    result[row[0]] = row[1]
                
                return result
        except Exception as e:
            print(f"خطأ في جلب المصاريف حسب التصنيف: {e}")
            return {}
    
    def get_total_expenses(self, start_date: str = None, 
                          end_date: str = None) -> float:
        """جلب إجمالي المصاريف"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                if start_date and end_date:
                    cursor.execute('''
                        SELECT SUM(amount) FROM expenses 
                        WHERE date BETWEEN ? AND ?
                    ''', (start_date, end_date))
                else:
                    cursor.execute('SELECT SUM(amount) FROM expenses')
                
                result = cursor.fetchone()[0]
                return result if result else 0.0
        except Exception as e:
            print(f"خطأ في جلب إجمالي المصاريف: {e}")
            return 0.0
