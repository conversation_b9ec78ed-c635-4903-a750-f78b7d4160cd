import os
from datetime import datetime, timedelta

try:
    from reportlab.lib.pagesizes import A4, letter
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch, cm
    from reportlab.lib import colors
    from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, Image
    from reportlab.platypus.flowables import HRFlowable
    from reportlab.lib.enums import TA_CENTER, TA_RIGHT, TA_LEFT
    from reportlab.pdfbase import pdfmetrics
    from reportlab.pdfbase.ttfonts import TTFont
    REPORTLAB_AVAILABLE = True
except ImportError:
    REPORTLAB_AVAILABLE = False

try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False

class PDFExporter:
    """كلاس لتصدير التقارير بصيغة PDF"""
    
    def __init__(self, database):
        self.db = database
        self.setup_fonts()
        
    def setup_fonts(self):
        """إعداد الخطوط للعربية"""
        try:
            # محاولة تسجيل خط عربي
            # يمكن تحميل خط عربي مثل Amiri أو Noto Sans Arabic
            # هنا نستخدم خط افتراضي
            pass
        except:
            pass
            
    def reshape_arabic_text(self, text):
        """تحويل النص العربي للعرض الصحيح في PDF"""
        if not text:
            return ""
        if ARABIC_SUPPORT:
            try:
                reshaped_text = arabic_reshaper.reshape(text)
                return get_display(reshaped_text)
            except:
                return text
        return text
            
    def create_monthly_report(self, year, month, filename=None):
        """إنشاء تقرير شهري"""
        if not REPORTLAB_AVAILABLE:
            raise ImportError("reportlab library is required for PDF export")

        if not filename:
            filename = f"expense_report_{year}_{month:02d}.pdf"
            
        # تحديد نطاق التاريخ
        start_date = f"{year}-{month:02d}-01"
        if month == 12:
            end_date = f"{year + 1}-01-01"
        else:
            end_date = f"{year}-{month + 1:02d}-01"
        
        # تحويل تاريخ النهاية لآخر يوم في الشهر
        end_datetime = datetime.strptime(end_date, "%Y-%m-%d") - timedelta(days=1)
        end_date = end_datetime.strftime("%Y-%m-%d")
        
        # جلب البيانات
        expenses = self.db.get_expenses(start_date, end_date)
        total_amount = self.db.get_total_expenses(start_date, end_date)
        category_data = self.db.get_expenses_by_category(start_date, end_date)
        
        # إنشاء المستند
        doc = SimpleDocTemplate(filename, pagesize=A4, rightMargin=2*cm, leftMargin=2*cm,
                              topMargin=2*cm, bottomMargin=2*cm)
        
        # قائمة العناصر
        story = []
        
        # الأنماط
        styles = getSampleStyleSheet()
        
        # نمط العنوان الرئيسي
        title_style = ParagraphStyle(
            'CustomTitle',
            parent=styles['Heading1'],
            fontSize=18,
            spaceAfter=30,
            alignment=TA_CENTER,
            textColor=colors.darkblue
        )
        
        # نمط العنوان الفرعي
        subtitle_style = ParagraphStyle(
            'CustomSubtitle',
            parent=styles['Heading2'],
            fontSize=14,
            spaceAfter=20,
            alignment=TA_RIGHT,
            textColor=colors.darkgreen
        )
        
        # نمط النص العادي
        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontSize=12,
            alignment=TA_RIGHT,
            spaceAfter=12
        )
        
        # العنوان الرئيسي
        month_names = {
            1: "يناير", 2: "فبراير", 3: "مارس", 4: "أبريل",
            5: "مايو", 6: "يونيو", 7: "يوليو", 8: "أغسطس",
            9: "سبتمبر", 10: "أكتوبر", 11: "نوفمبر", 12: "ديسمبر"
        }
        
        title_text = f"تقرير المصاريف الشهري - {month_names[month]} {year}"
        title = Paragraph(self.reshape_arabic_text(title_text), title_style)
        story.append(title)
        story.append(Spacer(1, 20))
        
        # معلومات التقرير
        report_info = f"تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}"
        story.append(Paragraph(self.reshape_arabic_text(report_info), normal_style))
        
        period_info = f"الفترة: من {start_date} إلى {end_date}"
        story.append(Paragraph(self.reshape_arabic_text(period_info), normal_style))
        story.append(Spacer(1, 20))
        
        # خط فاصل
        story.append(HRFlowable(width="100%", thickness=1, color=colors.grey))
        story.append(Spacer(1, 20))
        
        # ملخص الإحصائيات
        summary_title = Paragraph(self.reshape_arabic_text("ملخص الإحصائيات"), subtitle_style)
        story.append(summary_title)
        
        # جدول الإحصائيات
        stats_data = [
            [self.reshape_arabic_text("البيان"), self.reshape_arabic_text("القيمة")],
            [self.reshape_arabic_text("إجمالي المصاريف"), f"{total_amount:,.2f} ريال"],
            [self.reshape_arabic_text("عدد المصاريف"), str(len(expenses))],
            [self.reshape_arabic_text("متوسط المصروف"), f"{total_amount/len(expenses):,.2f} ريال" if expenses else "0 ريال"],
            [self.reshape_arabic_text("أكبر مصروف"), f"{max([exp['amount'] for exp in expenses]):,.2f} ريال" if expenses else "0 ريال"]
        ]
        
        stats_table = Table(stats_data, colWidths=[8*cm, 6*cm])
        stats_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), colors.darkblue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey])
        ]))
        
        story.append(stats_table)
        story.append(Spacer(1, 30))
        
        # المصاريف حسب التصنيف
        if category_data:
            category_title = Paragraph(self.reshape_arabic_text("المصاريف حسب التصنيف"), subtitle_style)
            story.append(category_title)
            
            category_table_data = [
                [self.reshape_arabic_text("التصنيف"), self.reshape_arabic_text("المبلغ"), self.reshape_arabic_text("النسبة")]
            ]
            
            for category, amount in category_data.items():
                percentage = (amount / total_amount * 100) if total_amount > 0 else 0
                category_table_data.append([
                    self.reshape_arabic_text(category),
                    f"{amount:,.2f} ريال",
                    f"{percentage:.1f}%"
                ])
            
            category_table = Table(category_table_data, colWidths=[6*cm, 4*cm, 3*cm])
            category_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkgreen),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('FONTSIZE', (0, 1), (-1, -1), 10),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey])
            ]))
            
            story.append(category_table)
            story.append(Spacer(1, 30))
        
        # تفاصيل المصاريف
        if expenses:
            details_title = Paragraph(self.reshape_arabic_text("تفاصيل المصاريف"), subtitle_style)
            story.append(details_title)
            
            # جدول المصاريف
            expenses_data = [
                [self.reshape_arabic_text("التاريخ"), self.reshape_arabic_text("الاسم"), 
                 self.reshape_arabic_text("التصنيف"), self.reshape_arabic_text("المبلغ")]
            ]
            
            for expense in expenses:
                expenses_data.append([
                    expense['date'],
                    self.reshape_arabic_text(expense['name']),
                    self.reshape_arabic_text(expense['category']),
                    f"{expense['amount']:,.2f} ريال"
                ])
            
            expenses_table = Table(expenses_data, colWidths=[3*cm, 6*cm, 3*cm, 3*cm])
            expenses_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.darkorange),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 10),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black),
                ('FONTSIZE', (0, 1), (-1, -1), 9),
                ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey])
            ]))
            
            story.append(expenses_table)
        
        # بناء المستند
        doc.build(story)
        return filename
