import sys
from datetime import datetime, timedelta
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                             QFrame, QGridLayout, QComboBox, QPushButton,
                             QScrollArea, QSizePolicy)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QFont, QPalette

try:
    import matplotlib.pyplot as plt
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    from matplotlib.figure import Figure
    import matplotlib.dates as mdates
    from matplotlib import font_manager
    MATPLOTLIB_AVAILABLE = True
except ImportError:
    MATPLOTLIB_AVAILABLE = False

try:
    import arabic_reshaper
    from bidi.algorithm import get_display
    ARABIC_SUPPORT = True
except ImportError:
    ARABIC_SUPPORT = False

class DashboardPage(QWidget):
    """صفحة لوحة المعلومات الرئيسية"""
    
    def __init__(self, database):
        super().__init__()
        self.db = database
        self.init_ui()
        self.setup_matplotlib()
        
    def init_ui(self):
        """إعداد واجهة المستخدم"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(20)
        
        # العنوان الرئيسي
        title = QLabel("📊 لوحة المعلومات")
        title.setObjectName("title")
        title.setAlignment(Qt.AlignRight)
        layout.addWidget(title)
        
        # فلتر الفترة الزمنية
        self.create_period_filter(layout)
        
        # بطاقات الإحصائيات
        self.create_stats_cards(layout)
        
        # الرسوم البيانية
        self.create_charts_section(layout)
        
        # تحديث البيانات
        self.refresh_data()
        
    def create_period_filter(self, parent_layout):
        """إنشاء فلتر الفترة الزمنية"""
        filter_frame = QFrame()
        filter_frame.setObjectName("card")
        filter_layout = QHBoxLayout(filter_frame)
        
        # تسمية الفلتر
        filter_label = QLabel("عرض البيانات لـ:")
        filter_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        
        # قائمة الفترات
        self.period_combo = QComboBox()
        self.period_combo.addItems([
            "اليوم",
            "الأسبوع الحالي", 
            "الشهر الحالي",
            "آخر 30 يوم",
            "آخر 3 أشهر",
            "السنة الحالية",
            "جميع الفترات"
        ])
        self.period_combo.setCurrentText("الشهر الحالي")
        self.period_combo.currentTextChanged.connect(self.refresh_data)
        
        # زر التحديث
        refresh_btn = QPushButton("🔄 تحديث")
        refresh_btn.clicked.connect(self.refresh_data)
        
        filter_layout.addWidget(filter_label)
        filter_layout.addWidget(self.period_combo)
        filter_layout.addWidget(refresh_btn)
        filter_layout.addStretch()
        
        parent_layout.addWidget(filter_frame)
        
    def create_stats_cards(self, parent_layout):
        """إنشاء بطاقات الإحصائيات"""
        stats_frame = QFrame()
        stats_layout = QGridLayout(stats_frame)
        stats_layout.setSpacing(15)
        
        # بطاقة إجمالي المصاريف
        self.total_card = self.create_stat_card("💰", "إجمالي المصاريف", "0 ريال", "#e74c3c")
        
        # بطاقة عدد المصاريف
        self.count_card = self.create_stat_card("📝", "عدد المصاريف", "0", "#3498db")
        
        # بطاقة متوسط المصروف
        self.avg_card = self.create_stat_card("📊", "متوسط المصروف", "0 ريال", "#27ae60")
        
        # بطاقة أكبر مصروف
        self.max_card = self.create_stat_card("⬆️", "أكبر مصروف", "0 ريال", "#f39c12")
        
        # إضافة البطاقات للشبكة
        stats_layout.addWidget(self.total_card, 0, 0)
        stats_layout.addWidget(self.count_card, 0, 1)
        stats_layout.addWidget(self.avg_card, 1, 0)
        stats_layout.addWidget(self.max_card, 1, 1)
        
        parent_layout.addWidget(stats_frame)
        
    def create_stat_card(self, icon, title, value, color):
        """إنشاء بطاقة إحصائية"""
        card = QFrame()
        card.setObjectName("stat_card")
        card.setStyleSheet(f"""
            QFrame#stat_card {{
                background-color: {color};
                border: none;
                border-radius: 15px;
                padding: 20px;
                margin: 5px;
            }}
            QFrame#stat_card QLabel {{
                color: white;
                font-weight: bold;
                background-color: transparent;
            }}
        """)
        
        layout = QVBoxLayout(card)
        layout.setAlignment(Qt.AlignCenter)
        
        # الأيقونة والعنوان
        header_layout = QHBoxLayout()
        
        icon_label = QLabel(icon)
        icon_label.setFont(QFont("Segoe UI", 24))
        icon_label.setAlignment(Qt.AlignCenter)
        
        title_label = QLabel(title)
        title_label.setFont(QFont("Segoe UI", 12, QFont.Bold))
        title_label.setAlignment(Qt.AlignRight)
        
        header_layout.addWidget(icon_label)
        header_layout.addStretch()
        header_layout.addWidget(title_label)
        
        # القيمة
        value_label = QLabel(value)
        value_label.setFont(QFont("Segoe UI", 18, QFont.Bold))
        value_label.setAlignment(Qt.AlignCenter)
        
        layout.addLayout(header_layout)
        layout.addWidget(value_label)
        
        # حفظ مرجع لتسمية القيمة للتحديث لاحقاً
        card.value_label = value_label
        
        return card
        
    def create_charts_section(self, parent_layout):
        """إنشاء قسم الرسوم البيانية"""
        charts_frame = QFrame()
        charts_frame.setObjectName("card")
        charts_layout = QVBoxLayout(charts_frame)

        # عنوان القسم
        charts_title = QLabel("📈 الرسوم البيانية")
        charts_title.setFont(QFont("Segoe UI", 16, QFont.Bold))
        charts_title.setAlignment(Qt.AlignRight)
        charts_layout.addWidget(charts_title)

        if MATPLOTLIB_AVAILABLE:
            # تخطيط الرسوم البيانية
            charts_grid = QHBoxLayout()

            # الرسم البياني الدائري للتصنيفات
            self.pie_chart = self.create_pie_chart()
            charts_grid.addWidget(self.pie_chart)

            # الرسم البياني الخطي للمصاريف اليومية
            self.line_chart = self.create_line_chart()
            charts_grid.addWidget(self.line_chart)

            charts_layout.addLayout(charts_grid)
        else:
            # رسالة في حالة عدم توفر matplotlib
            no_charts_label = QLabel("الرسوم البيانية غير متاحة\nيرجى تثبيت matplotlib")
            no_charts_label.setAlignment(Qt.AlignCenter)
            no_charts_label.setStyleSheet("color: #7f8c8d; font-size: 14px; padding: 50px;")
            charts_layout.addWidget(no_charts_label)

        parent_layout.addWidget(charts_frame)
        
    def create_pie_chart(self):
        """إنشاء الرسم البياني الدائري"""
        if not MATPLOTLIB_AVAILABLE:
            return QLabel("matplotlib غير متاح")

        figure = Figure(figsize=(6, 4), dpi=100)
        canvas = FigureCanvas(figure)
        canvas.setMinimumHeight(300)

        self.pie_ax = figure.add_subplot(111)
        self.pie_figure = figure

        return canvas

    def create_line_chart(self):
        """إنشاء الرسم البياني الخطي"""
        if not MATPLOTLIB_AVAILABLE:
            return QLabel("matplotlib غير متاح")

        figure = Figure(figsize=(6, 4), dpi=100)
        canvas = FigureCanvas(figure)
        canvas.setMinimumHeight(300)

        self.line_ax = figure.add_subplot(111)
        self.line_figure = figure

        return canvas
        
    def setup_matplotlib(self):
        """إعداد matplotlib للعربية"""
        if MATPLOTLIB_AVAILABLE:
            plt.rcParams['font.family'] = ['Tahoma', 'DejaVu Sans', 'Arial Unicode MS']
            plt.rcParams['axes.unicode_minus'] = False
        
    def get_date_range(self):
        """الحصول على نطاق التاريخ حسب الفلتر المحدد"""
        period = self.period_combo.currentText()
        today = datetime.now().date()
        
        if period == "اليوم":
            return today.strftime('%Y-%m-%d'), today.strftime('%Y-%m-%d')
        elif period == "الأسبوع الحالي":
            start = today - timedelta(days=today.weekday())
            end = start + timedelta(days=6)
            return start.strftime('%Y-%m-%d'), end.strftime('%Y-%m-%d')
        elif period == "الشهر الحالي":
            start = today.replace(day=1)
            if today.month == 12:
                end = today.replace(year=today.year + 1, month=1, day=1) - timedelta(days=1)
            else:
                end = today.replace(month=today.month + 1, day=1) - timedelta(days=1)
            return start.strftime('%Y-%m-%d'), end.strftime('%Y-%m-%d')
        elif period == "آخر 30 يوم":
            start = today - timedelta(days=30)
            return start.strftime('%Y-%m-%d'), today.strftime('%Y-%m-%d')
        elif period == "آخر 3 أشهر":
            start = today - timedelta(days=90)
            return start.strftime('%Y-%m-%d'), today.strftime('%Y-%m-%d')
        elif period == "السنة الحالية":
            start = today.replace(month=1, day=1)
            end = today.replace(month=12, day=31)
            return start.strftime('%Y-%m-%d'), end.strftime('%Y-%m-%d')
        else:  # جميع الفترات
            return None, None

    def refresh_data(self):
        """تحديث جميع البيانات في الصفحة"""
        start_date, end_date = self.get_date_range()

        # تحديث الإحصائيات
        self.update_stats(start_date, end_date)

        # تحديث الرسوم البيانية إذا كانت متاحة
        if MATPLOTLIB_AVAILABLE:
            self.update_pie_chart(start_date, end_date)
            self.update_line_chart(start_date, end_date)

    def update_stats(self, start_date, end_date):
        """تحديث بطاقات الإحصائيات"""
        # جلب البيانات
        expenses = self.db.get_expenses(start_date, end_date)
        total = self.db.get_total_expenses(start_date, end_date)

        # حساب الإحصائيات
        count = len(expenses)
        avg = total / count if count > 0 else 0
        max_expense = max([exp['amount'] for exp in expenses]) if expenses else 0

        # تحديث البطاقات
        self.total_card.value_label.setText(f"{total:,.0f} ريال")
        self.count_card.value_label.setText(str(count))
        self.avg_card.value_label.setText(f"{avg:,.0f} ريال")
        self.max_card.value_label.setText(f"{max_expense:,.0f} ريال")

    def update_pie_chart(self, start_date, end_date):
        """تحديث الرسم البياني الدائري"""
        if not MATPLOTLIB_AVAILABLE:
            return

        self.pie_ax.clear()

        # جلب البيانات حسب التصنيف
        category_data = self.db.get_expenses_by_category(start_date, end_date)

        if not category_data:
            self.pie_ax.text(0.5, 0.5, 'No Data Available',
                           horizontalalignment='center',
                           verticalalignment='center',
                           transform=self.pie_ax.transAxes,
                           fontsize=14)
        else:
            # تحضير البيانات
            categories = []
            amounts = []
            colors = ['#e74c3c', '#3498db', '#2ecc71', '#f39c12', '#9b59b6',
                     '#1abc9c', '#34495e', '#95a5a6']

            for i, (category, amount) in enumerate(category_data.items()):
                # تحويل النص العربي للعرض الصحيح إذا كان متاحاً
                if ARABIC_SUPPORT:
                    try:
                        reshaped_text = arabic_reshaper.reshape(category)
                        display_text = get_display(reshaped_text)
                        categories.append(display_text)
                    except:
                        categories.append(category)
                else:
                    categories.append(category)
                amounts.append(amount)

            # رسم المخطط الدائري
            wedges, texts, autotexts = self.pie_ax.pie(amounts, labels=categories,
                                                      colors=colors[:len(categories)],
                                                      autopct='%1.1f%%',
                                                      startangle=90)

            # تحسين النصوص
            for autotext in autotexts:
                autotext.set_color('white')
                autotext.set_fontweight('bold')

        # تعيين العنوان
        title_text = "Expenses by Category"
        if ARABIC_SUPPORT:
            try:
                title_text = arabic_reshaper.reshape("المصاريف حسب التصنيف")
                title_text = get_display(title_text)
            except:
                pass

        self.pie_ax.set_title(title_text, fontsize=14, fontweight='bold', pad=20)

        self.pie_figure.tight_layout()
        self.pie_chart.draw()

    def update_line_chart(self, start_date, end_date):
        """تحديث الرسم البياني الخطي"""
        if not MATPLOTLIB_AVAILABLE:
            return

        self.line_ax.clear()

        # جلب البيانات
        expenses = self.db.get_expenses(start_date, end_date)

        if not expenses:
            self.line_ax.text(0.5, 0.5, 'No Data Available',
                            horizontalalignment='center',
                            verticalalignment='center',
                            transform=self.line_ax.transAxes,
                            fontsize=14)
        else:
            # تجميع البيانات حسب التاريخ
            daily_expenses = {}
            for expense in expenses:
                date = expense['date']
                if date in daily_expenses:
                    daily_expenses[date] += expense['amount']
                else:
                    daily_expenses[date] = expense['amount']

            # ترتيب البيانات حسب التاريخ
            sorted_dates = sorted(daily_expenses.keys())
            dates = [datetime.strptime(date, '%Y-%m-%d').date() for date in sorted_dates]
            amounts = [daily_expenses[date] for date in sorted_dates]

            # رسم المخطط الخطي
            self.line_ax.plot(dates, amounts, marker='o', linewidth=2,
                            markersize=6, color='#3498db')

            # تنسيق المحاور
            self.line_ax.set_xlabel('Date', fontsize=12, fontweight='bold')
            self.line_ax.set_ylabel('Amount (SAR)', fontsize=12, fontweight='bold')

            # تنسيق تواريخ المحور السيني
            if len(dates) > 10:
                self.line_ax.xaxis.set_major_locator(mdates.WeekdayLocator())
                self.line_ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))
            else:
                self.line_ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))

            # تدوير تسميات التاريخ
            plt.setp(self.line_ax.xaxis.get_majorticklabels(), rotation=45)

            # إضافة شبكة
            self.line_ax.grid(True, alpha=0.3)

        # تعيين العنوان
        title_text = "Daily Expenses"
        if ARABIC_SUPPORT:
            try:
                title_text = arabic_reshaper.reshape("المصاريف اليومية")
                title_text = get_display(title_text)
            except:
                pass

        self.line_ax.set_title(title_text, fontsize=14, fontweight='bold', pad=20)

        self.line_figure.tight_layout()
        self.line_chart.draw()
