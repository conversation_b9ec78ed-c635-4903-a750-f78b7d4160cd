# 💰 برنامج إدارة المصاريف الشخصية

برنامج احترافي لإدارة المصاريف الشخصية باستخدام Python و PyQt5 مع دعم كامل للغة العربية وواجهة مستخدم عصرية.

## ✨ المميزات

### 🎨 واجهة المستخدم
- تصميم عصري مسطح (Flat Design) بألوان هادئة
- دعم كامل للغة العربية مع اتجاه RTL
- واجهة متجاوبة تتكيف مع أحجام الشاشات المختلفة
- شريط جانبي للتنقل السهل بين الصفحات
- أيقونات واضحة ومعبرة

### 📊 لوحة المعلومات
- عرض إحصائيات شاملة (إجمالي المصاريف، العدد، المتوسط، أكبر مصروف)
- رسوم بيانية تفاعلية:
  - رسم دائري لتوزيع المصاريف حسب التصنيف
  - رسم خطي للمصاريف اليومية
- فلاتر زمنية متنوعة (يومي، أسبوعي، شهري، سنوي)

### ➕ إدارة المصاريف
- إضافة مصاريف جديدة مع جميع التفاصيل:
  - الاسم والمبلغ
  - التصنيف (طعام، مواصلات، ترفيه، إلخ)
  - التاريخ مع تقويم تفاعلي
  - ملاحظات اختيارية
- تعديل وحذف المصاريف الموجودة
- جدول تفاعلي لعرض جميع المصاريف

### 📄 التقارير
- تصدير تقارير شهرية بصيغة PDF
- تقارير مفصلة تشمل:
  - ملخص الإحصائيات
  - المصاريف حسب التصنيف
  - تفاصيل جميع المصاريف
- تصميم احترافي للتقارير مع دعم العربية

### 🗄️ قاعدة البيانات
- قاعدة بيانات SQLite محلية
- تخزين آمن للبيانات
- نسخ احتياطي تلقائي

## 🛠️ متطلبات التشغيل

### متطلبات النظام
- Windows 10/11, macOS 10.14+, أو Linux
- Python 3.7 أو أحدث
- ذاكرة RAM: 512 MB كحد أدنى
- مساحة تخزين: 100 MB

### المكتبات المطلوبة
```
PyQt5==5.15.10
matplotlib==3.7.2
reportlab==4.0.4
arabic-reshaper==3.0.0
python-bidi==0.4.2
Pillow==10.0.0
```

## 📥 التثبيت والتشغيل

### 1. تحميل المشروع
```bash
git clone https://github.com/your-username/personal-expense-manager.git
cd personal-expense-manager
```

### 2. إنشاء بيئة افتراضية (اختياري ولكن مُوصى به)
```bash
python -m venv expense_manager_env

# على Windows
expense_manager_env\Scripts\activate

# على macOS/Linux
source expense_manager_env/bin/activate
```

### 3. تثبيت المكتبات المطلوبة
```bash
pip install -r requirements.txt
```

### 4. تشغيل التطبيق
```bash
python app.py
```

## 🚀 كيفية الاستخدام

### البداية السريعة
1. شغل التطبيق باستخدام `python app.py`
2. ستظهر شاشة ترحيبية ثم النافذة الرئيسية
3. ابدأ بإضافة مصروف جديد من صفحة "إضافة مصروف"
4. راجع الإحصائيات في "لوحة المعلومات"
5. أدر مصاريفك من صفحة "إدارة المصاريف"
6. صدر التقارير من قسم "التقارير"

### التنقل في التطبيق
- **لوحة المعلومات**: عرض الإحصائيات والرسوم البيانية
- **إضافة مصروف**: إضافة مصاريف جديدة
- **إدارة المصاريف**: عرض وتعديل وحذف المصاريف
- **التقارير**: تصدير التقارير الشهرية

### اختصارات لوحة المفاتيح
- `Ctrl + S`: حفظ المصروف الجديد
- `Ctrl + R`: مسح النموذج
- `F2` أو `Enter`: تعديل المصروف المحدد
- `Delete`: حذف المصروف المحدد
- `F5`: تحديث البيانات

## 📁 هيكل المشروع

```
personal-expense-manager/
├── app.py                    # ملف تشغيل التطبيق الرئيسي
├── main_window.py           # النافذة الرئيسية
├── dashboard_page.py        # صفحة لوحة المعلومات
├── add_expense_page.py      # صفحة إضافة المصاريف
├── manage_expenses_page.py  # صفحة إدارة المصاريف
├── database.py              # إدارة قاعدة البيانات
├── pdf_exporter.py          # تصدير التقارير PDF
├── styles.qss               # ملف الأنماط
├── requirements.txt         # المكتبات المطلوبة
├── README.md               # هذا الملف
└── expenses.db             # قاعدة البيانات (تُنشأ تلقائياً)
```

## 🎨 التخصيص

### تغيير الألوان
يمكنك تعديل ملف `styles.qss` لتغيير ألوان التطبيق:
```css
/* تغيير اللون الأساسي */
#sidebar {
    background-color: #your-color;
}
```

### إضافة تصنيفات جديدة
التصنيفات تُدار تلقائياً من قاعدة البيانات. يمكنك إضافة تصنيفات جديدة من خلال تعديل ملف `database.py`.

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**1. خطأ في تثبيت PyQt5**
```bash
# على Ubuntu/Debian
sudo apt-get install python3-pyqt5

# على macOS
brew install pyqt5
```

**2. مشاكل في عرض العربية**
تأكد من تثبيت الخطوط العربية على نظامك.

**3. خطأ في إنشاء PDF**
تأكد من وجود صلاحيات الكتابة في مجلد التطبيق.

## 🤝 المساهمة

نرحب بمساهماتكم! يمكنكم:
1. الإبلاغ عن الأخطاء
2. اقتراح مميزات جديدة
3. تحسين الكود
4. ترجمة التطبيق لغات أخرى

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 📞 الدعم

إذا واجهت أي مشاكل أو لديك أسئلة:
- افتح issue جديد في GitHub
- راسلنا على البريد الإلكتروني
- راجع قسم الأسئلة الشائعة

---

**تم تطوير هذا التطبيق بحب ❤️ لمساعدتك في إدارة مصاريفك بكفاءة**
